import {createRouter, createWebHistory} from 'vue-router';
import store from "@/store";

import Login from './views/auth/login.vue';
import ForgotPassword from './views/auth/forgot.vue';
import Verify from './views/auth/verify.vue';
import VerifyLogin from './views/auth/verify-login.vue';

import Dashboard from './views/dashboard/index.vue';
import SummaryAverages from './views/dashboard/summary_averages.vue';
import GamesSummary from './views/dashboard/games_summary.vue';
import SportBetsSummary from './views/dashboard/sports_bets_summary.vue';
import VirtualBetsSummary from './views/dashboard/virtual_bets_summary.vue';
import SoftGamingBetsSummary from './views/dashboard/softgaming_bets_summary.vue';

import Profiles from '@/views/customer/index.vue';
import ProfilesAdd from '@/views/customer/add.vue';
import ProfileSearch from '@/views/customer/customer_search.vue';
import ProfilesOld from '@/views/customer/customers_old.vue';
import ProfilesSelfExclusion from '@/views/customer/customers_self_excusion.vue';

import Taxes from '@/views/taxes/index.vue';
import TaxSummary from '@/views/taxes/tax_summary.vue';
import TaxPayments from '@/views/taxes/tax_payments.vue';

// BulkSMS
import OutBox from '@/views/bulk_sms/outbox.vue';
import BulkSMS from '@/views/bulk_sms/send_message.vue';
import ScheduledSMS from '@/views/bulk_sms/scheduled.vue';

// PayBills, add, edit
import Paybills from '@/views/system/paybills/index.vue';
import PaybillsAdd from '@/views/system/paybills/add.vue';
import PaybillsEdit from '@/views/system/paybills/edit.vue';

// Auth Channels
import AuthChannels from '@/views/system/auth_channels/index.vue';
import AuthChannelsAdd from '@/views/system/auth_channels/add.vue';
import AuthChannelsEdit from '@/views/system/auth_channels/edit.vue';

import Pragmatic from '@/views/pragmatic/index.vue';

// Promotions
import Promotions from '@/views/promotions/index.vue';
import PromotionsAdd from "@/views/promotions/add.vue";
import PromotionsEdit from "@/views/promotions/edit.vue";
import Campaigns from '@/views/promotions/campaigns.vue';
import Leaderboard from '@/views/promotions/leaderboard.vue'
import GreenerMonday from '@/views/promotions/greener_monday.vue';

// System
import SystemUsers from '@/views/system/users/index.vue';
import SystemUsersAdd from '@/views/system/users/add.vue';
import SystemUsersEdit from '@/views/system/users/edit.vue';

import SystemRoles from '@/views/system/roles/index.vue';
import SystemRolesAdd from '@/views/system/roles/add.vue';
import SystemRolesEdit from '@/views/system/roles/edit.vue';

import SystemIndex from '@/views/system/permissions/index.vue';

// Transactions
import Deposits from '@/views/transactions/deposits.vue';
import Withdrawals from '@/views/transactions/withdrawals.vue';
import WalletApprovals from '@/views/transactions/wallet_approvals.vue';
import RiskApprovalsUpload from '@/views/transactions/risk_approvals_upload.vue';
import SystemTransactions from "@/views/transactions/system_transactions.vue";

// Reports
import SpecialReports from "@/views/reports/special_reports.vue"; // Reports


// Sports Book
import Fixtures from "@/views/sports_book/fixtures.vue";
import FixturesManualResult from "@/views/sports_book/fixtures_manual_result.vue";
import FixturesManualResultArchive from "@/views/sports_book/fixtures_manual_result_archive.vue";
import OddsLive from "@/views/sports_book/odds_live.vue";
import Tournaments from "@/views/sports_book/tournaments.vue";

// Bets
import SportsBets from "@/views/bets/sports_bets.vue";
import SportsBetSlip from "@/views/bets/sports_bet_slip.vue";
import CasinoBets from "@/views/bets/casino_bets.vue";
import VirtualBets from "@/views/bets/virtual_bets.vue";
import SoftGaming from "@/views/bets/soft_gaming.vue";
import BetAudits from "@/views/bets/bet_audits.vue";
import BetLimits from "@/views/bets/bet_limits.vue";
import BetGames from "@/views/bets/bet_games.vue";
import BetGameCategories from "@/views/bets/bet_game_categories.vue";
import IPs from "@/views/bets/bet_ips.vue";
import MenuHighlights from "@/views/bets/menu_highlights.vue";

import Markets from "@/views/sports_book/markets.vue";

// import Logs from '@/views/logs/logs.vue'


const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {path: '/', name: 'login', component: Login, meta: {guest: true}},
        {path: '/forgot', name: 'forgot-password', component: ForgotPassword, meta: {guest: true}},
        {path: '/verify', name: 'verify', component: Verify, meta: {guest: true}},
        {path: '/verify-login/:username?', name: 'verify-login', component: VerifyLogin, meta: {guest: true}},
        {
            path: '/app',
            children: [
                {path: '', name: 'dashboard', component: Dashboard, meta: {requiresAuth: true}},
                {path: 'summary-averages', name: 'summary-averages', component: SummaryAverages, meta: {requiresAuth: true}},
                {path: 'games-summary', name: 'games-summary', component: GamesSummary, meta: {requiresAuth: true}},
                {path: 'sports-bets-summary', name: 'sports-bets-summary', component: SportBetsSummary, meta: {requiresAuth: true}},
                {path: 'virtual-bets-summary', name: 'virtual-bets-summary', component: VirtualBetsSummary, meta: {requiresAuth: true}},
                {path: 'soft-gaming-bets-summary', name: 'soft-gaming-bets-summary', component: SoftGamingBetsSummary, meta: {requiresAuth: true}},

                // Profiles
                {path: 'customers', name: 'customers', component: Profiles, meta: {requiresAuth: true}},
                {path: 'customer/add', name: 'customer-add', component: ProfilesAdd, meta: {requiresAuth: true}},
                {path: 'customer/search', name: 'customer-search', component: ProfileSearch, meta: {requiresAuth: true}},
                {path: 'customers/old', name: 'customers-old', component: ProfilesOld, meta: {requiresAuth: true}},
                {path: 'customers/self-exclusion', name: 'customers-self-exclusion', component: ProfilesSelfExclusion, meta: {requiresAuth: true}},

                // Deposits
                {path: 'deposits', name: 'deposits', component: Deposits, meta: {requiresAuth: true}},
                {path: 'manual/reconciliation', name: 'manual-reconciliation', component: Deposits, meta: {requiresAuth: true}},
                // Withdrawals
                {path: 'withdrawals', name: 'withdrawals', component: Withdrawals, meta: {requiresAuth: true}},
                {path: 'system-transactions', name: 'system-transactions', component: SystemTransactions, meta: {requiresAuth: true}},
                {path: 'wallet-approvals', name: 'wallet-approvals', component: WalletApprovals, meta: {requiresAuth: true}},
                {path: 'risk-approvals-uploads', name: 'risk-approvals-uploads', component: RiskApprovalsUpload, meta: {requiresAuth: true}},


                // Transactions
                // {path: 'transactions', name: 'transactions', component: Transactions, meta: {requiresAuth: true}},

                // Fixtures
                {path: 'fixtures', name: 'fixtures', component: Fixtures, meta: {requiresAuth: true}},
                {path: 'fixtures-manual-result', name: 'fixtures-manual-result', component: FixturesManualResult, meta: {requiresAuth: true}},
                {path: 'fixtures-manual-result/archive', name: 'fixtures-manual-result-archive', component: FixturesManualResultArchive, meta: {requiresAuth: true}},
                {path: 'odds-live', name: 'odds-live', component: OddsLive, meta: {requiresAuth: true}},
                {path: 'markets', name: 'markets', component: Markets, meta: {requiresAuth: true}},
                {path: 'tournaments', name: 'tournaments', component: Tournaments, meta: {requiresAuth: true}},

                // Bets
                {path: 'sports-bets', name: 'sports-bets', component: SportsBets, meta: {requiresAuth: true}},
                {path: 'sports-bets/slip/:id?', name: 'sport-bet-slip', component: SportsBetSlip, meta: {requiresAuth: true}},
                {path: 'virtual-bets', name: 'virtual-bets', component: VirtualBets, meta: {requiresAuth: true}},
                {path: 'soft-gaming', name: 'soft-gaming', component: SoftGaming, meta: {requiresAuth: true}},
                {path: 'casino-bets', name: 'casino-bets', component: CasinoBets, meta: {requiresAuth: true}},
                {path: 'bet-audits', name: 'bet-audits', component: BetAudits, meta: {requiresAuth: true}},
                {path: 'bet-limits', name: 'bet-limits', component: BetLimits, meta: {requiresAuth: true}},
                {path: 'bet-games', name: 'bet-games', component: BetGames, meta: {requiresAuth: true}},
                {path: 'bets-games-categories', name: 'bets-games-categories', component: BetGameCategories, meta: {requiresAuth: true}},

                {path: 'bet-ips', name: 'bet-ips', component: IPs, meta: {requiresAuth: true}},
                {path: 'menu-highlights', name: 'menu-highlights', component: MenuHighlights, meta: {requiresAuth: true}},

                // Reports
                {path: 'special-reports', name: 'special-reports', component: SpecialReports, meta: {requiresAuth: true}},

                // Taxes
                {path: 'taxes', name: 'taxes', component: Taxes, meta: {requiresAuth: true}},
                {path: 'taxes/summary', name: 'tax-summary', component: TaxSummary, meta: {requiresAuth: true}},
                {path: 'taxes/payments', name: 'tax-payments', component: TaxPayments, meta: {requiresAuth: true}},

                // BulkSMS
                {path: 'bulk-sms', name: 'bulk-sms', component: BulkSMS, meta: {requiresAuth: true}},
                {path: 'outbox', name: 'outbox', component: OutBox, meta: {requiresAuth: true}},
                {path: 'scheduled-sms', name: 'scheduled-sms', component: ScheduledSMS, meta: {requiresAuth: true}},

                // Promotions
                {path: 'promotions', name: 'promotions', component: Promotions, meta: {requiresAuth: true}},
                {path: 'promotions/add', name: 'promotions-add', component: PromotionsAdd, meta: {requiresAuth: true}},
                {path: 'promotions/edit', name: 'promotions-edit', component: PromotionsEdit, meta: {requiresAuth: true}},
                // add campaign
                {path: 'campaigns', name: 'campaigns', component: Campaigns, meta: {requiresAuth: true}},
                // {path: 'campaigns/add', name: 'campaigns-add', component: PromotionsAdd, meta: {requiresAuth: true}},
                // {path: 'campaigns/edit', name: 'campaigns-edit', component: PromotionsEdit, meta: {requiresAuth: true}},
                {path: 'leaderboard', name: 'leaderboard', component: Leaderboard, meta: {requiresAuth: true}},
                {path: 'greener-monday', name: 'greener-monday', component: GreenerMonday, meta: {requiresAuth: true}},


                /// System : Users, Roles, Permissions
                {path: 'system/users/:id?', name: 'system-users', component: SystemUsers, meta: {requiresAuth: true}},
                {path: 'system-users/add', name: 'system-users-add', component: SystemUsersAdd, meta: {requiresAuth: true}},
                {path: 'system-users/edit', name: 'system-users-edit', component: SystemUsersEdit, meta: {requiresAuth: true}},

                {path: 'system/roles', name: 'roles', component: SystemRoles, meta: {requiresAuth: true}},
                {path: 'system/roles/add', name: 'roles-add', component: SystemRolesAdd, meta: {requiresAuth: true}},
                {path: 'system/roles/edit', name: 'roles-edit', component: SystemRolesEdit, meta: {requiresAuth: true}},

                { path: 'system-users/permissions', name: 'permissions', component: SystemIndex, meta: { requiresAuth: true } },



                /// PayBills
                {path: 'paybills', name: 'paybills', component: Paybills, meta: {requiresAuth: true}},
                {path: 'paybills/add', name: 'paybills-add', component: PaybillsAdd, meta: {requiresAuth: true}},
                {path: 'paybills/edit', name: 'paybills-edit', component: PaybillsEdit, meta: {requiresAuth: true}},

                /// Auth Channels
                {path: 'auth_channels', name: 'auth-channels', component: AuthChannels, meta: {requiresAuth: true}},
                {path: 'auth-channels/add', name: 'auth-channels-add', component: AuthChannelsAdd, meta: {requiresAuth: true}},
                {path: 'auth-channels/edit', name: 'auth-channels-edit', component: AuthChannelsEdit, meta: {requiresAuth: true}},

                /// Pragmatic
                {path: 'pragmatic', name: 'pragmatic', component: Pragmatic, meta: {requiresAuth: true}},

            ]
        }
    ]
})

router.beforeEach((to, _from, next) => {
    if (to.matched.some((record) => record.meta.requiresAuth)) {
        if (store.getters.isAuthenticated) {
            next();
            return;
        }
        next("/");
    } else {
        next();
    }
});

router.beforeEach((to, _from, next) => {
    if (to.matched.some((record) => record.meta.guest)) {
        if (store.getters.isAuthenticated) {
            next("/app");
            return;
        }
        next();
    } else {
        next();
    }
});
export default router
