<template>
<div class="py-2 bg-white relative overflow-hidden">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />
    
      <!-- Main Bets Table (Slide 0) -->
      <div class="block py-4 bg-white overflow-x-auto transition-all duration-300 ease-in-out"
           :class="{'translate-x-0 opacity-100': !isBetSlipOpen, '-translate-x-full opacity-0 absolute inset-0': isBetSlipOpen}">
        <!-- Filters - Redesigned to be more compact -->
        <div class="px-2 pb-2 flex flex-wrap gap-2 w-full">
          <!-- Bet Ref -->
          <div class="w-48">
            <label class="block text-xs font-bold text-gray-700">Bet Ref</label>
            <input type="text" placeholder="Ref" @keyup.enter="applyFilters()"
                   class="block w-full px-2 py-1 text-xs text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.bet_reference">
          </div>

          <!-- Bet ID -->
          <div class="w-48">
            <label class="block text-xs font-bold text-gray-700">Bet ID</label>
            <input type="text" placeholder="ID" @keyup.enter="applyFilters()"
                   class="block w-full px-2 py-1 text-xs text-gray-700 border rounded-md shadow-sm"
                   v-model="moreParams.bet_id">
          </div>

          <!-- Bet Amount -->
          <div class="w-48">
            <label class="block text-xs font-bold text-gray-700">Bet Amount</label>
            <div class="flex gap-1">
              <input type="number" placeholder="Min" @keyup.enter="applyFilters()"
                     class="block w-full px-2 py-1 text-xs text-gray-700 border rounded-md shadow-sm"
                     v-model="moreParams.stake_amount_min">
              <input type="number" placeholder="Max" @keyup.enter="applyFilters()"
                     class="block w-full px-2 py-1 text-xs text-gray-700 border rounded-md shadow-sm"
                     v-model="moreParams.stake_amount_max">
            </div>
          </div>

          <!-- Apply Button -->
          <div class="flex items-end">
            <button class="px-3 py-1 bg-primary text-white text-xs rounded-md" @click="applyFilters()">
              Apply
            </button>
          </div>
        </div>

        <auto-table
          :headers="tableHeaders"
          :data="bets"
      :loading="isLoading"
      :has-actions="true"
      :pagination="true"
      :server-side-pagination="true"
      :total-items="total"
      :items-per-page="limit"
      :current-page-prop="offset"
      :items-per-page-options="[10, 25, 50, 100]"
      :show-items-count="true"
      :decimal-places="decimalPlaces"
      :exclude-columns="excludeColumns"
      :header-styles="headerStyles"
      :column-styles="columnStyles"
      @page-change="handlePageChange"
      @items-per-page-change="handleLimitChange"
    >
     <!-- Bet ID Column -->
     <template #bet_id="{ item }">
            <div>{{ item.bet_id }}</div>
          </template>

          <!-- Bet Reference Column -->
          <template #bet_reference="{ item }">
            <div>
              <span class="font-extrabold">{{ item.bet_reference }}</span>
              <br>
              <span style="font-size: 11px;">{{ item.bet_attribution }}</span>
            </div>
          </template>

          <!-- Customer Column -->
          <template #customer="{ item }">
            <div>{{ getExtraDataValue(item, "msisdn") }}</div>
          </template>

          <!-- Stake Column -->
          <template #bet_amount="{ item }">
            <div>Ksh. {{ formatNumber(item.bet_amount) }}</div>
          </template>

          <!-- Possible Win Column -->
          <template #possible_win="{ item }">
            <div>
              <strong>Ksh. {{ formatNumber(item.possible_win) }}</strong>
              <br>
              <span style="font-size: 11px;">W.Tax: Ksh. {{ formatNumber(item.witholding_tax) }}</span>
            </div>
          </template>

          <!-- Bet Type Column -->
          <template #bet_type="{ item }">
            <div class="status-badge" 
              :class="{
                'bg-green-500': item.bet_type === '0',
                'bg-blue-500': item.bet_type === '1',
                'bg-cyan-500': item.bet_type === '2'
              }">
              {{ betTypeText(item.bet_type) }}
            </div>
          </template>

          <!-- Total Games Column -->
          <template #total_games="{ item }">
            <div class="text-center font-bold">{{ item.total_games }}</div>
          </template>

          <!-- Total Odd Column -->
          <template #total_odd="{ item }">
            <div class="text-center">
              <span class="status-badge" :class="getOddsClass(item.total_odd)">
                {{ formatNumber(item.total_odd) }}
              </span>
            </div>
          </template>

          <!-- Status Column -->
          <template #bet_status="{ item }">
            <div class="status-badge"
            :class="getStatusClass(item.bet_status)">
              {{ processedText(item.bet_status) }}
            </div>
          </template>

          <!-- Date Column -->
          <template #created_at="{ item }">
            <span style="font-size: 11px; color: grey">{{ moment(item.created_at).format('llll') }}</span>
          </template>

      <template #actions="{ item, index }">
            <div class="relative z-10">
              <action-dropdown 
                v-if="(parseInt(item.match_status) !== 0) && (parseInt(item.match_status) !== null)"
                button-text="Actions" 
                :show-text="false"
                button-class="z-50"
                menu-class="z-50 origin-top-right"
                :menu-width="48"
              >
                <action-item
                  text="View Bet Slip"
                  color="blue"
                  @click="viewBetSlip(item)"
                >
                  <template #icon>
                    <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </template>
                </action-item>
                
                <action-item
                  text="View Details"
                  color="blue"
                  @click="viewBetDetails(item)"
                >
                  <template #icon>
                    <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </template>
                </action-item>
              </action-dropdown>
              
              <button v-else class="px-3 py-1 flex items-center space-x-1">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                     stroke="#808080" class="w-6 h-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                        d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </button>
            </div>
          </template>

    </auto-table>

        <!-- Main AutoTable component -->
       
      </div>

      <!-- Bet Slip View (Slide 1) -->
      <div class="block py-4 bg-white overflow-x-auto transition-all duration-300 ease-in-out"
           :class="{'translate-x-0 opacity-100': isBetSlipOpen, 'translate-x-full opacity-0 absolute inset-0': !isBetSlipOpen}">
        <div class="p-4">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">
              Bet Slip Details
              <span v-if="selectBet" class="text-sm font-normal text-gray-500 ml-2">
                ({{ selectBet.bet_reference || 'No Reference' }})
              </span>
            </h3>
            
            <button @click="closeBetSlip" class="text-gray-500 hover:text-gray-700 focus:outline-none">
              <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div v-if="selectBet" class="mb-4 p-4 bg-gray-50 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p class="text-sm text-gray-500">Bet Reference</p>
                <p class="font-bold">{{ selectBet.bet_reference || 'N/A' }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Stake</p>
                <p class="font-bold">{{ formatNumber(selectBet.bet_amount) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Possible Win</p>
                <p class="font-bold">{{ formatNumber(selectBet.possible_win) }}</p>
              </div>
            </div>
          </div>
          
          <div v-if="bet_slips.length > 0">
            <auto-table
              :headers="slipTableHeaders"
              :data="bet_slips"
              :total-items="modal_total"
              :items-per-page="modal_limit"
              :current-page-prop="modal_offset"
              :server-side-pagination="true"
              :pagination="modal_total > modal_limit"
              :show-items-count="true"
              :items-per-page-options="[10, 25, 50]"
              @page-change="handleModalPageChange"
              @items-per-page-change="handleModalLimitChange"
            >
              <!-- Slip ID Column -->
              <template #slip_id="{ item }">
                <div>{{ item.slip_id }}</div>
              </template>
              
              <!-- Teams Column -->
              <template #teams="{ item }">
                <div v-if="item.extra_data">
                  <strong>{{ getExtraDataValues(item).competitor1 }}</strong>
                  vs
                  <strong>{{ getExtraDataValues(item).competitor2 }}</strong>
                </div>
              </template>
              
              <!-- Bet Type Column -->
              <template #bet_type="{ item }">
                <div>{{ item.bet_type }}</div>
              </template>
              
              <!-- Pick Column -->
              <template #pick="{ item }">
                <div>{{ item.pick }}</div>
              </template>

              <!-- Odds Column -->
              <template #odd_value="{ item }">
                <div class="text-center">
                  <span class="status-badge" :class="getOddsClass(item.odd_value)">
                    {{ formatNumber(item.odd_value) }}
                  </span>
                </div>
              </template>

              <!-- Winning Outcome Column -->
              <template #winning_outcome="{ item }">
                <div>{{ item.winning_outcome || 'Not Resulted' }}</div>
              </template>
              
              <!-- Scores Column -->
              <template #scores="{ item }">
                <div class="text-xs">
                  <div>HT: {{ item.ht_scores || '-:-' }}</div>
                  <div>FT: {{ item.ft_scores || '-:-' }}</div>
                  <div>ET: {{ item.et_scores || '-:-' }}</div>
                </div>
              </template>
              
              <!-- Status Column -->
              <template #status="{ item }">
                <div class="status-badge" :class="getStatusClass(item.status)">
                  {{ getStatusText(item.status) }}
                </div>
              </template>
              
              <!-- Start Time Column -->
              <template #start_time="{ item }">
                <span style="font-size: 11px; color: grey">{{ moment(item.start_time).format('llll') }}</span>
              </template>
            </auto-table>
          </div>
          
          <div v-else class="py-8 text-center text-gray-500">
            No bet slip data available
          </div>
        </div>
      </div>
    
    <!-- Bet Details Modal - Placed outside the carousel -->
    <div v-if="isDetailsModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <!-- Enhanced Header with Bet Reference, MSISDN and IP -->
          <div class="border-b pb-4 mb-4">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-xl font-bold">Bet Details</h3>
                <div class="mt-1 text-sm text-gray-600 flex flex-col space-y-1">
                  <div><span class="font-medium">Ref:</span> {{ detailsData.bet_reference }}</div>
                  <div><span class="font-medium">MSISDN:</span> {{ getExtraDataValue(detailsData, "msisdn") }}</div>
                  <div><span class="font-medium">IP:</span> {{ detailsData.ip_address || getExtraDataValue(detailsData, "ip") }}</div>
                </div>
              </div>
              <button @click="isDetailsModalOpen = false" class="text-gray-500 hover:text-gray-700">
                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          
          <div v-if="detailsData" class="space-y-4">
            <!-- Bet Information -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 bg-gray-50 p-4 rounded-lg">
              <div>
                <p class="text-sm text-gray-500">Bet ID</p>
                <p class="font-bold">{{ detailsData.bet_id }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Stake</p>
                <p class="font-bold">Ksh. {{ formatNumber(detailsData.bet_amount) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Possible Win</p>
                <p class="font-bold">Ksh. {{ formatNumber(detailsData.possible_win) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Withholding Tax</p>
                <p class="font-bold">Ksh. {{ formatNumber(detailsData.witholding_tax) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Excise Tax</p>
                <p class="font-bold">Ksh. {{ formatNumber(detailsData.excise_tax) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Total Odd</p>
                <p class="font-bold">{{ formatNumber(detailsData.total_odd) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Bet Type</p>
                <p class="font-bold">{{ betTypeText(detailsData.bet_type) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Status</p>
                <p class="font-bold">{{ processedText(detailsData.bet_status) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Date</p>
                <p class="font-bold">{{ moment(detailsData.created_at).format('lll') }}</p>
              </div>
            </div>
            
            <!-- Browser Details Section -->
            <div v-if="detailsData.browser_details" class="mt-4">
              <h4 class="font-semibold text-lg mb-2">Browser Details</h4>
              <div class="bg-gray-50 p-4 rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div v-for="(value, key) in parseBrowserDetails(detailsData.browser_details)" :key="key">
                    <p class="text-sm text-gray-500">{{ formatKey(key) }}</p>
                    <p class="font-medium">{{ value }}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Extra Data Section -->
            <div v-if="detailsData.extra_data" class="mt-4">
              <h4 class="font-semibold text-lg mb-2">Additional Information</h4>
              
              <!-- Bonus Information -->
              <div v-if="getBonusAmount(detailsData) > 0 || getCashAmount(detailsData) > 0" class="bg-gray-50 p-4 rounded-lg mb-3">
                <h5 class="font-medium mb-2">Bonus Information</h5>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p class="text-sm text-gray-500">Bonus Amount</p>
                    <p class="font-medium">Ksh. {{ formatNumber(getBonusAmount(detailsData)) }}</p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">Cash Amount</p>
                    <p class="font-medium">Ksh. {{ formatNumber(getCashAmount(detailsData)) }}</p>
                  </div>
                </div>
              </div>
              
              <!-- Risk Information -->
              <div v-if="Object.keys(getRiskInfo(detailsData)).length > 0" class="bg-gray-50 p-4 rounded-lg mb-3">
                <h5 class="font-medium mb-2">Risk Information</h5>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div v-for="(value, key) in getRiskInfo(detailsData)" :key="key">
                    <p class="text-sm text-gray-500">{{ formatKey(key) }}</p>
                    <p class="font-medium">{{ value }}</p>
                  </div>
                </div>
              </div>
              
              <!-- Issued Bonus Information -->
              <div v-if="Object.keys(getIssuedInfo(detailsData)).length > 0" class="bg-gray-50 p-4 rounded-lg">
                <h5 class="font-medium mb-2">Issued Bonus</h5>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div v-for="(value, key) in getIssuedInfo(detailsData)" :key="key">
                    <p class="text-sm text-gray-500">{{ formatKey(key) }}</p>
                    <p class="font-medium">{{ value }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';
import moment from "moment";
import {mapActions} from "vuex";
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import { AutoTable, CustomLoading, ActionDropdown, ActionItem } from '@/components/common';

export default {
  components: {
    VueDatePicker,
    AutoTable,
    CustomLoading,
    ActionDropdown,
    ActionItem
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      loading: true,
      fullPage: true,
      total: 0,
      limit: 100,
      offset: 1,
      tableHeaders: [
        { key: 'bet_id', label: 'Bet ID', align: 'center' },
        { key: 'bet_reference', label: 'Bet Reference', align: 'center' },
        { key: 'customer', label: 'Customer', align: 'center' },
        { key: 'bet_amount', label: 'Stake', align: 'center' },
        { key: 'possible_win', label: 'Possible Win', align: 'left' },
        { key: 'bet_type', label: 'Bet Type', align: 'center' },
        { key: 'total_games', label: 'Total Games', align: 'center' },
        { key: 'total_odd', label: 'Total Odd', align: 'center' },
        { key: 'bet_status', label: 'Status', align: 'center' },
        { key: 'created_at', label: 'Date', align: 'center' },
      ],
      checkboxes: {
        mobile_number: false,
        winning_amount: false,
        odds: false,
        selections: false,
        bet_amount: false,
        bet_reference: false,
        bet_id: false,
      },
      showDropdown: [],
      viewModelOpen: false,
      //
      isModalOpen: false, // This will control modal visibility
      bet_slips: [], // Your data
      modal_total: 0,
      modal_limit: 100,
      modal_offset: 1,
      showBetSlips: true, // This will control which view is shown in the modal

      //
      selectBet: {},
      bet_slip_params: {
        bet_id: '',
        sport_id: '',
        status: '',
        start: '',
        end: '',
        page: '',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
      },

      //
      bets: [],
      //
      phone: '',
      moreParams: {
        bet_id: '',
        game_type: 'sports',
        stake_amount_min: '',
        stake_amount_max: '',
        winning_amount_min: '',
        winning_amount_max: '',
        odds_min: '',
        odds_max: '',
        selections_min: '',
        selections_max: '',
        bet_type: '',
        selection_type: '',
        profile_id: '',
        mobile_number: this.$store.state.customer.msisdn || '',
        status: '',
        sort: '',
        start: '',
        end: '',
        page: '',
        limit: "100",
        timestamp: 'timestamp',
        skip_cache: '',
      },

      // Add headers for the modal table
      slipTableHeaders: [
        { key: 'slip_id', label: 'ID', align: 'center' },
        { key: 'teams', label: 'Teams', align: 'center' },
        { key: 'bet_type', label: 'Bet Type', align: 'center' },
        { key: 'pick', label: 'Pick', align: 'center' },
        { key: 'odd_value', label: 'Odds', align: 'center' },
        { key: 'winning_outcome', label: 'Winning Outcome', align: 'center' },
        { key: 'scores', label: 'Scores', align: 'center' },
        { key: 'status', label: 'Status', align: 'center' },
        { key: 'start_time', label: 'Start Time', align: 'center' },
      ],
      isDetailsModalOpen: false,
      detailsData: {},
      isBetSlipOpen: false,
    }
  },

  mounted() {
     this.moreParams.mobile_number = this.$store.state.customer.msisdn || ''
    // this.setBets()
  },
  methods: {
    ...mapActions(["getSportsBets", "getSportsBetSlips", "toggleSideMenu",]),

    // Get row actions for AutoTable
    getRowActions(item) {
      if ((parseInt(item.match_status) !== 0) && (parseInt(item.match_status) !== null)) {
        return [
          {
            label: 'View Details',
            action: () => this.viewBetDetails(item),
            icon: 'fas fa-info-circle'
          }
        ];
      }
      return [];
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setBets(this.phone);
    },

    // Handle modal items per page change
    handleModalLimitChange(newLimit) {
      this.modal_limit = newLimit;
      this.modal_offset = 1;
      // Reload modal data with new limit
      this.viewBetDetails(this.currentBetItem);
    },

    async selectDate() {
      let vm = this
      vm.moreParams.start = vm.formatDate(this.date[0])
      vm.moreParams.end = vm.formatDate(this.date[1])
      vm.moreParams.timestamp = Date.now()

      await vm.setBets(this.phone)
    },

    formatDate(date) {
      var d = new Date(date),
          month = '' + (d.getMonth() + 1),
          day = '' + d.getDate(),
          year = d.getFullYear();

      if (month.length < 2)
        month = '0' + month;
      if (day.length < 2)
        day = '0' + day;

      return [year, month, day].join('-');
    },

    // Pagination and dropdowns
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setBets(this.phone)
    },

    toggleDropdown(index) {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = i === index ? !this.showDropdown[i] : false;
      }
    },

    closeDropdown() {
      for (let i = 0; i < this.showDropdown.length; i++) {
        this.showDropdown[i] = false;
      }
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.deposit = data;
    },

    async viewBetDetails(data) {
      let app = this
      app.closeDropdown()
      
      // Set the details data
      app.detailsData = data
      
      // Open the details modal
      app.isDetailsModalOpen = true
      
      console.log("Opening bet details modal for bet ID:", data.bet_id)
    },

    applyFilters() {
      // console.log("applyFilters", JSON.stringify(this.$store.state.customer.msisdn))
      // this.moreParams.mobile_number = this.$store.state.customer.msisdn || ''
      this.moreParams.page = ''
      this.moreParams.offset = ''
      console.log("applyFilters>>>", JSON.stringify(this.moreParams))
      this.setBets(this.phone)
    },

    async setBets(num) {
      let app = this
      app.isLoading = true
      app.moreParams.mobile_number = num
      app.phone = num

      let response = await this.getSportsBets(app.moreParams)

      app.bets = []
      app.total = 0
      if (response.status === 200) {
        app.bets = response.message.result

        if (response.message.record_count !== 0) {
          // Ensure total is a number by using parseInt
          app.total = parseInt(response.message.record_count)
        }

        app.showDropdown = []
        for (let i = 0; i < app.bets.length; i++) {
          app.showDropdown.push(false)
        }
      } else {
        app.bets = [];
        app.total = 0;
      }
      app.isLoading = false
    },

    // View bet slip
    async viewBetSlip(bet) {
      let app = this
      app.isLoading = true
      app.closeDropdown() // Close any open dropdowns

      const params = new URLSearchParams();
      app.bet_slip_params.bet_id = bet.bet_id
      app.selectBet = bet

      for (const key in app.bet_slip_params) {
        if (app.bet_slip_params.hasOwnProperty(key)) {
          params.append(key, app.bet_slip_params[key]);
        }
      }

      const queryString = params.toString();
      let response = await this.getSportsBetSlips(queryString)

      if (response.status === 200) {
        app.bet_slips = response.message.result || []
        // Ensure modal_total is a number by using parseInt
        app.modal_total = parseInt(response.message.record_count || 0)

        app.showDropdown = []
        for (let i = 0; i < app.bet_slips.length; i++) {
          app.showDropdown.push(false)
        }
        
        // Make sure to set this flag to true AFTER data is loaded
        app.isBetSlipOpen = true
        console.log("Bet slips loaded:", app.bet_slips.length, "items")
      } else {
        app.bet_slips = [];
        app.modal_total = 0;
        app.isBetSlipOpen = true // Still open the panel even if no data
        console.log("No bet slips found or error occurred")
      }
      app.isLoading = false
    },

    // Format currency with commas and put negative figures in brackets (standardized from master)
    formatCurrency(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Format numbers with commas (no decimal places for counts) and put negative figures in brackets
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },


    getExtraDataValue(data, key) {
      if (!data.extra_data) return null; // Check if extra_data exists
      try {
        const extraData = JSON.parse(data.extra_data); // Parse JSON string
        // console.log("extraData", extraData);
        return extraData[key] ?? null; // Return the requested key or null if not found
      } catch (error) {
        console.error("Error parsing extra_data:", error);
        return null;
      }
    },

    statusText(status) {
      if (status === "0") return "Not Started";
      if (status === "1") return "In Progress";
      return "Finished"; // For other statuses
    },
    betTypeText(status) {
      if (status === "0") return "Cash Bet";
      if (status === "1") return "Bonus Bet";
      if (status === "2") return "Free Bet";
    },
    processedText(status) {
      if (status === "0") return "PENDING";
      if (status === "1") return "WON";
      if (status === "3") return "LOST";
      if (status === "7") return "VOIDED";
      if (status === "9") return "CANCELED";
    },
    // Method to parse extra_data and return as an object
    parseExtraData(extraData) {
      try {
        return JSON.parse(extraData);  // Parse the extra_data JSON string into an object
      } catch (error) {
        console.error('Error parsing extra_data:', error);
        return {};  // Return an empty object if parsing fails
      }
    },
    // Method to extract and return specific values from parsed extra_data
    getExtraDataValues(item) {
      if (!item.extra_data) {
        return { competitor1: 'Unknown', competitor2: 'Unknown' };
      }
      
      try {
        // Try to parse as JSON if it's a string
        const extraData = typeof item.extra_data === 'string' 
          ? JSON.parse(item.extra_data) 
          : item.extra_data;
        
        return {
          competitor1: extraData.competitor1 || extraData.home_team || 'Team 1',
          competitor2: extraData.competitor2 || extraData.away_team || 'Team 2'
        };
      } catch (e) {
        console.error('Error parsing extra data:', e);
        return { competitor1: 'Unknown', competitor2: 'Unknown' };
      }
    },

    // Add method for modal pagination
    handleModalPageChange(page) {
      // Ensure page is a number
      this.modal_offset = parseInt(page)
      this.bet_slip_params.page = page.toString()
      this.viewBetSlip(this.selectBet)
    },
    getOddsClass(odds) {
      const oddValue = parseFloat(odds);
      
      if (isNaN(oddValue)) return 'bg-gray-500';
      if (oddValue < 5.0) return 'bg-blue-400';
      if (oddValue < 10.0) return 'bg-green-400';
      if (oddValue < 20.0) return 'bg-yellow-500';
      if (oddValue < 50.0) return 'bg-orange-500';
      if (oddValue < 100.0) return 'bg-red-500';
      if (oddValue >= 100.0) return 'bg-purple-500';
      return 'bg-purple-600'; // 10.0 and above
    },
    // Parse browser details JSON
    parseBrowserDetails(browserDetails) {
      try {
        return JSON.parse(browserDetails);
      } catch (error) {
        console.error('Error parsing browser details:', error);
        return {};
      }
    },
    
    // Format keys for display
    formatKey(key) {
      return key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
    },
    
    // Get bonus amount from extra data
    getBonusAmount(item) {
      try {
        const extraData = JSON.parse(item.extra_data);
        return extraData.bonus?.bonus_amount || 0;
      } catch (error) {
        return 0;
      }
    },
    
    // Get cash amount from extra data
    getCashAmount(item) {
      try {
        const extraData = JSON.parse(item.extra_data);
        return extraData.bonus?.cash_amount || 0;
      } catch (error) {
        return 0;
      }
    },
    
    // Get risk information
    getRiskInfo(item) {
      try {
        const extraData = JSON.parse(item.extra_data);
        return extraData.risk || {};
      } catch (error) {
        return {};
      }
    },
    
    // Get issued bonus information
    getIssuedInfo(item) {
      try {
        const extraData = JSON.parse(item.extra_data);
        return extraData.issued || {};
      } catch (error) {
        return {};
      }
    },
    // Add a method to close the modal
    closeModal() {
      this.isModalOpen = false;
      // Reset after animation completes
      setTimeout(() => {
        this.showBetSlips = true;
      }, 300);
    },
    // Add a method to close the bet slip view
    closeBetSlip() {
      this.isBetSlipOpen = false;
      // Clear data when closing
      setTimeout(() => {
        this.bet_slips = [];
        this.modal_total = 0;
      }, 300); // Clear after transition completes
    },
    // Method to get status class
    getStatusClass(status) {
      const statusMap = {
        '0': 'bg-neutral-500', // Pending - gray
        '1': 'bg-green-500',   // Won - green
        '3': 'bg-red-500',     // Lost - red
        '7': 'bg-gray-500',    // Voided - gray
        '9': 'bg-gray-500'     // Canceled - gray (updated to gray)
      };
      return statusMap[status] || 'bg-gray-500';
    },

    // Method to get status text
    getStatusText(status) {
      const statusMap = {
        '0': 'PENDING',
        '1': 'WON',
        '3': 'LOST',
        '7': 'VOIDED',
        '9': 'CANCELED'
      };
      return statusMap[status] || 'Unknown';
    },
    // Debug method to log the current state
    debugState() {
      console.log({
        isBetSlipOpen: this.isBetSlipOpen,
        betSlipsCount: this.bet_slips.length,
        modalTotal: this.modal_total,
        selectBet: this.selectBet ? this.selectBet.bet_id : null
      });
    },
  },
}
</script>

<style scoped>
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}

/* Transition styles */
.transition-all {
  transition-property: all;
}

/* Ensure dropdowns are on top */
.relative {
  position: relative;
}

:deep(.action-dropdown) {
  z-index: 9999 !important;
  position: absolute !important;
}

/* Override any overflow settings that might be clipping the dropdown */
.absolute.top-0.bottom-0.left-0.right-0.overflow-auto {
  overflow: visible !important;
}

/* Make sure action dropdowns have the highest z-index */
:deep(.action-dropdown .absolute) {
  z-index: 9999 !important;
  position: fixed !important;
}

.duration-300 {
  transition-duration: 300ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Add this to your existing styles */
:deep(.data-table table) {
  margin-bottom: 2.5rem !important; /* equivalent to mb-10 in Tailwind */
}
</style>
